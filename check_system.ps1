# Computer System Check Script
Write-Host "=== Computer System Information Check ===" -ForegroundColor Green

# Get system boot time and uptime
try {
    $os = Get-CimInstance -ClassName Win32_OperatingSystem
    $bootTime = $os.LastBootUpTime
    $uptime = (Get-Date) - $bootTime

    Write-Host "`nSystem Runtime Information:" -ForegroundColor Yellow
    Write-Host "Last Boot Time: $bootTime"
    Write-Host "System Uptime: $($uptime.Days) days $($uptime.Hours) hours $($uptime.Minutes) minutes"
    Write-Host "System Install Date: $($os.InstallDate)"

} catch {
    Write-Host "Error getting system runtime info: $($_.Exception.Message)" -ForegroundColor Red
}

# Get ALL boot/shutdown records via event logs
try {
    Write-Host "`nAll Boot Records:" -ForegroundColor Yellow

    # Get ALL boot events (no limit)
    Write-Host "Searching for all boot events in system log..."
    $allBootEvents = Get-WinEvent -FilterHashtable @{LogName='System'; ID=6005} -ErrorAction SilentlyContinue

    if ($allBootEvents) {
        Write-Host "`nAll boot records (from newest to oldest):"
        $counter = 1
        $allBootEvents | ForEach-Object {
            Write-Host "  $counter. Boot time: $($_.TimeCreated)"
            $counter++
        }
        Write-Host "`nTotal boot count (all records): $($allBootEvents.Count)" -ForegroundColor Green

        # Calculate usage statistics
        $firstBoot = $allBootEvents | Select-Object -Last 1
        $lastBoot = $allBootEvents | Select-Object -First 1
        $daysSinceFirstBoot = ((Get-Date) - $firstBoot.TimeCreated).Days

        Write-Host "`nUsage Statistics:" -ForegroundColor Cyan
        Write-Host "First boot recorded: $($firstBoot.TimeCreated)"
        Write-Host "Latest boot: $($lastBoot.TimeCreated)"
        Write-Host "Days since first boot: $daysSinceFirstBoot"
        if ($daysSinceFirstBoot -gt 0) {
            $avgBootsPerDay = [math]::Round($allBootEvents.Count / $daysSinceFirstBoot, 2)
            Write-Host "Average boots per day: $avgBootsPerDay"
        }

    } else {
        Write-Host "Cannot get boot event records"
    }

    # Also try to get shutdown events
    Write-Host "`nSearching for shutdown events..."
    $shutdownEvents = Get-WinEvent -FilterHashtable @{LogName='System'; ID=6006} -ErrorAction SilentlyContinue
    if ($shutdownEvents) {
        Write-Host "Total shutdown count: $($shutdownEvents.Count)" -ForegroundColor Green
    }

} catch {
    Write-Host "Error getting boot/shutdown records: $($_.Exception.Message)" -ForegroundColor Red
}

# Get hardware information
try {
    Write-Host "`nHardware Information:" -ForegroundColor Yellow

    # Motherboard info
    $motherboard = Get-CimInstance -ClassName Win32_BaseBoard
    Write-Host "Motherboard Manufacturer: $($motherboard.Manufacturer)"
    Write-Host "Motherboard Model: $($motherboard.Product)"
    Write-Host "Motherboard Serial: $($motherboard.SerialNumber)"

    # CPU info
    $cpu = Get-CimInstance -ClassName Win32_Processor
    Write-Host "Processor: $($cpu.Name)"

    # Memory info
    $memory = Get-CimInstance -ClassName Win32_PhysicalMemory
    $totalMemory = ($memory | Measure-Object -Property Capacity -Sum).Sum / 1GB
    Write-Host "Total Memory: $([math]::Round($totalMemory, 2)) GB"

} catch {
    Write-Host "Error getting hardware info: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
