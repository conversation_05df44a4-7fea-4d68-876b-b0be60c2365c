# Computer System Check Script
Write-Host "=== Computer System Information Check ===" -ForegroundColor Green

# Get system boot time and uptime
try {
    $os = Get-CimInstance -ClassName Win32_OperatingSystem
    $bootTime = $os.LastBootUpTime
    $uptime = (Get-Date) - $bootTime

    Write-Host "`nSystem Runtime Information:" -ForegroundColor Yellow
    Write-Host "Last Boot Time: $bootTime"
    Write-Host "System Uptime: $($uptime.Days) days $($uptime.Hours) hours $($uptime.Minutes) minutes"
    Write-Host "System Install Date: $($os.InstallDate)"

} catch {
    Write-Host "Error getting system runtime info: $($_.Exception.Message)" -ForegroundColor Red
}

# Get boot/shutdown count via event logs
try {
    Write-Host "`nBoot/Shutdown Records:" -ForegroundColor Yellow

    # Get recent boot events
    $bootEvents = Get-WinEvent -FilterHashtable @{LogName='System'; ID=6005} -MaxEvents 20 -ErrorAction SilentlyContinue
    if ($bootEvents) {
        Write-Host "Recent 20 boot records:"
        $bootEvents | ForEach-Object {
            Write-Host "  Boot time: $($_.TimeCreated)"
        }
        Write-Host "Total boot count (recent records): $($bootEvents.Count)"
    } else {
        Write-Host "Cannot get boot event records"
    }

} catch {
    Write-Host "Error getting boot/shutdown records: $($_.Exception.Message)" -ForegroundColor Red
}

# Get hardware information
try {
    Write-Host "`nHardware Information:" -ForegroundColor Yellow

    # Motherboard info
    $motherboard = Get-CimInstance -ClassName Win32_BaseBoard
    Write-Host "Motherboard Manufacturer: $($motherboard.Manufacturer)"
    Write-Host "Motherboard Model: $($motherboard.Product)"
    Write-Host "Motherboard Serial: $($motherboard.SerialNumber)"

    # CPU info
    $cpu = Get-CimInstance -ClassName Win32_Processor
    Write-Host "Processor: $($cpu.Name)"

    # Memory info
    $memory = Get-CimInstance -ClassName Win32_PhysicalMemory
    $totalMemory = ($memory | Measure-Object -Property Capacity -Sum).Sum / 1GB
    Write-Host "Total Memory: $([math]::Round($totalMemory, 2)) GB"

} catch {
    Write-Host "Error getting hardware info: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
