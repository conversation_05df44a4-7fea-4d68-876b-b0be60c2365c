# Detailed Boot Records Report
$reportFile = "boot_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

Write-Host "=== Detailed Boot Records Analysis ===" -ForegroundColor Green
Write-Host "Report will be saved to: $reportFile" -ForegroundColor Yellow

# Start building report content
$report = @()
$report += "=========================================="
$report += "    DETAILED BOOT RECORDS REPORT"
$report += "=========================================="
$report += "Generated: $(Get-Date)"
$report += ""

# Get system information
try {
    $os = Get-CimInstance -ClassName Win32_OperatingSystem
    $motherboard = Get-CimInstance -ClassName Win32_BaseBoard
    
    $report += "SYSTEM INFORMATION:"
    $report += "-------------------"
    $report += "Computer Name: $($env:COMPUTERNAME)"
    $report += "OS Version: $($os.Caption) $($os.Version)"
    $report += "System Install Date: $($os.InstallDate)"
    $report += "Last Boot Time: $($os.LastBootUpTime)"
    $report += "Motherboard: $($motherboard.Manufacturer) $($motherboard.Product)"
    $report += "Serial Number: $($motherboard.SerialNumber)"
    $report += ""
    
} catch {
    $report += "Error getting system info: $($_.Exception.Message)"
}

# Get ALL boot events
try {
    Write-Host "Analyzing all boot events..." -ForegroundColor Yellow
    
    # Try multiple event IDs for boot events
    $bootEventIDs = @(6005, 6009, 12)  # Different boot event IDs
    $allBootEvents = @()
    
    foreach ($eventID in $bootEventIDs) {
        try {
            $events = Get-WinEvent -FilterHashtable @{LogName='System'; ID=$eventID} -ErrorAction SilentlyContinue
            if ($events) {
                $allBootEvents += $events
                Write-Host "Found $($events.Count) events with ID $eventID"
            }
        } catch {
            # Continue to next event ID
        }
    }
    
    # Remove duplicates and sort by time
    $uniqueBootEvents = $allBootEvents | Sort-Object TimeCreated -Unique -Descending
    
    $report += "BOOT EVENTS ANALYSIS:"
    $report += "--------------------"
    $report += "Total unique boot events found: $($uniqueBootEvents.Count)"
    $report += ""
    
    if ($uniqueBootEvents.Count -gt 0) {
        $report += "COMPLETE BOOT HISTORY (Newest to Oldest):"
        $report += "==========================================="
        
        $counter = 1
        foreach ($event in $uniqueBootEvents) {
            $dayOfWeek = $event.TimeCreated.DayOfWeek
            $report += "$counter. $($event.TimeCreated) ($dayOfWeek) - Event ID: $($event.Id)"
            $counter++
        }
        
        $report += ""
        $report += "USAGE STATISTICS:"
        $report += "----------------"
        $firstBoot = $uniqueBootEvents | Select-Object -Last 1
        $lastBoot = $uniqueBootEvents | Select-Object -First 1
        $totalDays = ((Get-Date) - $firstBoot.TimeCreated).TotalDays
        
        $report += "First recorded boot: $($firstBoot.TimeCreated)"
        $report += "Most recent boot: $($lastBoot.TimeCreated)"
        $report += "Total days of usage: $([math]::Round($totalDays, 1))"
        
        if ($totalDays -gt 0) {
            $avgBootsPerDay = [math]::Round($uniqueBootEvents.Count / $totalDays, 2)
            $report += "Average boots per day: $avgBootsPerDay"
        }
        
        # Daily usage pattern
        $report += ""
        $report += "DAILY USAGE PATTERN:"
        $report += "-------------------"
        $dailyBoots = $uniqueBootEvents | Group-Object {$_.TimeCreated.Date} | Sort-Object Name -Descending
        foreach ($day in $dailyBoots) {
            $report += "$($day.Name): $($day.Count) boots"
        }
    }
    
} catch {
    $report += "Error analyzing boot events: $($_.Exception.Message)"
}

# Get shutdown events
try {
    Write-Host "Analyzing shutdown events..." -ForegroundColor Yellow
    $shutdownEvents = Get-WinEvent -FilterHashtable @{LogName='System'; ID=6006} -ErrorAction SilentlyContinue
    
    $report += ""
    $report += "SHUTDOWN EVENTS:"
    $report += "---------------"
    if ($shutdownEvents) {
        $report += "Total shutdown events: $($shutdownEvents.Count)"
        $report += ""
        $report += "Recent shutdowns:"
        $shutdownEvents | Select-Object -First 10 | ForEach-Object {
            $report += "  $($_.TimeCreated)"
        }
    } else {
        $report += "No shutdown events found"
    }
    
} catch {
    $report += "Error getting shutdown events: $($_.Exception.Message)"
}

# Save report to file
try {
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Host "`nReport saved successfully to: $reportFile" -ForegroundColor Green
} catch {
    Write-Host "Error saving report: $($_.Exception.Message)" -ForegroundColor Red
}

# Display summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
$report | ForEach-Object { Write-Host $_ }

Write-Host "`n=== Analysis Complete ===" -ForegroundColor Green
